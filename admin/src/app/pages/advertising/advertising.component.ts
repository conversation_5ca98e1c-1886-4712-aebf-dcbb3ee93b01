import {Component, ElementRef, inject, ViewChild, ViewEncapsulation} from '@angular/core';
import {FormBuilder, ReactiveFormsModule, Validators} from '@angular/forms';
import {AdvertisingService} from "@/services/advertising.service";
import {FileService} from "@/services/file.service";
import {PhotopreviewComponent} from "@/components/photopreview/photopreview.component";
import {environment} from "../../../environments/environment";
import { AdminDialogComponent } from '@/components/admin-dialog/admin-dialog.component';
// import { FlatpickrDefaultsInterface } from 'angularx-flatpickr';
// import {SharedModule} from "../../../shared.module";
// @ts-ignore
@Component({
    selector: 'app-advertising',
    imports: [
        ReactiveFormsModule,
        PhotopreviewComponent,
        AdminDialogComponent
        // SharedModule
    ],
    templateUrl: './advertising.component.html',
    styleUrl: './advertising.component.scss',
    encapsulation: ViewEncapsulation.None
})
export class AdvertisingComponent {
  @ViewChild('dialog') dialog!: ElementRef<HTMLDialogElement>;
  @ViewChild(AdminDialogComponent) adminDialog!: AdminDialogComponent;

  fb = inject(FormBuilder);
  advertisingService = inject(AdvertisingService);
  fileService = inject(FileService);
  form = this.fb.group({
    id: null,
    active: true,
    advertising: false,
    title: [null, Validators.required],
    description: [null, Validators.required],
    image: [null, Validators.required],
    link: [null, Validators.required],
    type: [1],
    freq: [1],
    date: [null],
  })
  items: any

  // dateTime: FlatpickrDefaultsInterface = {
  //   dateFormat: 'Y-m-d',
  //   monthSelectorType: 'dropdown',
  //   inline: true
  // };

  ngOnInit() {
    this.getAll()
    this.setupFormSubscriptions()
  }

  setupFormSubscriptions() {
    // Subscribe to advertising checkbox changes to enable/disable type and freq fields
    this.form.get('advertising')?.valueChanges.subscribe(isAdvertising => {
      if (isAdvertising) {
        this.form.get('type')?.enable()
        this.form.get('freq')?.enable()
      } else {
        this.form.get('type')?.disable()
        this.form.get('freq')?.disable()
      }
    })

    // Initial state - disable type and freq if advertising is false
    if (!this.form.get('advertising')?.value) {
      this.form.get('type')?.disable()
      this.form.get('freq')?.disable()
    }
  }

  getAll() {
    this.advertisingService.getAll().subscribe((res: any) => this.items = res)
  }

  closeDialog() {
    this.form.reset();
    this.dialog.nativeElement.close()
  }

  showDialog() {
    this.dialog.nativeElement.showModal()
  }

  uploadFile(e: Event) {
    const target = e.target as HTMLInputElement;
    if(!target.files) return

    this.fileService.uploadToTempFolder(target.files).subscribe((res: any) => {
      this.form.controls.image.patchValue(res[0])
    })
  }

  edit(item: any) {
    this.form.patchValue(item)
    this.showDialog()
  }

  async remove(id: number) {
    const confirmed = await this.adminDialog.showConfirm('Вы уверены, что хотите удалить эту рекламу?');
    if (!confirmed) return;

    this.advertisingService.remove(id).subscribe(() => this.getAll())
  }

  onSubmit() {
    this.advertisingService.create(this.form.value).subscribe(() => {
      this.closeDialog()
      this.getAll()
    })
  }

  protected readonly environment = environment;
}
